// PayPal configuration
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID!;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET!;
const PAYPAL_ENVIRONMENT = process.env.PAYPAL_ENVIRONMENT || 'production';

// PayPal API base URL
const PAYPAL_BASE_URL = PAYPAL_ENVIRONMENT === 'production'
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

// Get access token for PayPal API
async function getAccessToken() {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');

  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    throw new Error(`Failed to get access token: ${response.statusText}`);
  }

  const data = await response.json();
  return data.access_token;
}

// PayPal subscription plan IDs (created via setup script)
export const PAYPAL_PLANS = {
  PRO: 'P-4JP2277389389900MNBJSQJA',
  PLUS: 'P-3WP44074FT188045VNBJSQJI',
};

// PayPal plan configurations
export const PLAN_CONFIGS = {
  PRO: {
    name: 'Pro Plan',
    description: 'Perfect for growing HR teams with advanced features',
    price: '9.99',
    currency: 'USD',
    interval: 'MONTH',
    intervalCount: 1,
    features: {
      jobsPerMonth: 50,
      resumesPerJob: 100,
      totalResumes: 5000,
      dataRetention: '180 days',
    }
  },
  PLUS: {
    name: 'Plus Plan', 
    description: 'Perfect for bulk hiring with maximum limits',
    price: '19.99',
    currency: 'USD',
    interval: 'MONTH',
    intervalCount: 1,
    features: {
      jobsPerMonth: 100,
      resumesPerJob: 100,
      totalResumes: 10000,
      dataRetention: '180 days',
    }
  }
};

// Helper function to create subscription plans (used by setup script)
export async function createSubscriptionPlan(planType: 'PRO' | 'PLUS') {
  const config = PLAN_CONFIGS[planType];

  const planRequest = {
    product_id: 'PROD-4F0541520E7172144', // Created by setup script
    name: config.name,
    description: config.description,
    status: 'ACTIVE',
    billing_cycles: [
      {
        frequency: {
          interval_unit: config.interval,
          interval_count: config.intervalCount,
        },
        tenure_type: 'REGULAR',
        sequence: 1,
        total_cycles: 0, // 0 means infinite
        pricing_scheme: {
          fixed_price: {
            value: config.price,
            currency_code: config.currency,
          },
        },
      },
    ],
    payment_preferences: {
      auto_bill_outstanding: true,
      setup_fee_failure_action: 'CONTINUE',
      payment_failure_threshold: 3,
    },
    taxes: {
      percentage: '0',
      inclusive: false,
    },
  };

  try {
    const accessToken = await getAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/plans`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'PayPal-Request-Id': `plan-${planType}-${Date.now()}`,
      },
      body: JSON.stringify(planRequest),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to create ${planType} plan: ${response.statusText} - ${errorData}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating PayPal subscription plan:', error);
    throw error;
  }
}

// Helper function to create a subscription
export async function createSubscription(planId: string, returnUrl: string, cancelUrl: string) {
  const subscriptionRequest = {
    plan_id: planId,
    start_time: new Date(Date.now() + 60000).toISOString(), // Start 1 minute from now
    subscriber: {
      name: {
        given_name: 'John',
        surname: 'Doe',
      },
      email_address: '<EMAIL>',
    },
    application_context: {
      brand_name: 'The Consult Now',
      locale: 'en-US',
      shipping_preference: 'NO_SHIPPING',
      user_action: 'SUBSCRIBE_NOW',
      payment_method: {
        payer_selected: 'PAYPAL',
        payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED',
      },
      return_url: returnUrl,
      cancel_url: cancelUrl,
    },
  };

  try {
    const accessToken = await getAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'PayPal-Request-Id': `subscription-${Date.now()}`,
      },
      body: JSON.stringify(subscriptionRequest),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to create subscription: ${response.statusText} - ${errorData}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating PayPal subscription:', error);
    throw error;
  }
}

// Helper function to get subscription details
export async function getSubscription(subscriptionId: string) {
  try {
    const accessToken = await getAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to get subscription: ${response.statusText} - ${errorData}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting PayPal subscription:', error);
    throw error;
  }
}

// Helper function to cancel subscription
export async function cancelSubscription(subscriptionId: string, reason: string) {
  try {
    const accessToken = await getAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        reason: reason,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to cancel subscription: ${response.statusText} - ${errorData}`);
    }

    // Cancel endpoint returns 204 No Content on success
    return { success: true };
  } catch (error) {
    console.error('Error cancelling PayPal subscription:', error);
    throw error;
  }
}

// Helper function to verify webhook signature
export function verifyWebhookSignature(
  headers: Record<string, string>,
  body: string,
  webhookId: string
): boolean {
  // PayPal webhook verification logic
  // This is a simplified version - in production, you'd use PayPal's webhook verification
  const authAlgo = headers['paypal-auth-algo'];
  const transmission = headers['paypal-transmission-id'];
  const certId = headers['paypal-cert-id'];
  const signature = headers['paypal-transmission-sig'];
  const timestamp = headers['paypal-transmission-time'];
  
  // For now, return true - implement proper verification in production
  return true;
}

// Helper function to map PayPal plan to tier
export function mapPlanToTier(planId: string): 'Free' | 'Pro' | 'Plus' {
  if (planId === PAYPAL_PLANS.PRO) return 'Pro';
  if (planId === PAYPAL_PLANS.PLUS) return 'Plus';
  return 'Free';
}

// Helper function to get plan ID from tier
export function getTierPlanId(tier: 'Pro' | 'Plus'): string {
  return tier === 'Pro' ? PAYPAL_PLANS.PRO : PAYPAL_PLANS.PLUS;
}

// Types for PayPal webhook events
export interface PayPalWebhookEvent {
  id: string;
  event_type: string;
  resource_type: string;
  summary: string;
  resource: {
    id: string;
    status?: string;
    plan_id?: string;
    subscriber?: {
      email_address: string;
      name: {
        given_name: string;
        surname: string;
      };
    };
    billing_info?: {
      outstanding_balance: {
        currency_code: string;
        value: string;
      };
      cycle_executions: Array<{
        tenure_type: string;
        sequence: number;
        cycles_completed: number;
        cycles_remaining: number;
        current_pricing_scheme: {
          fixed_price: {
            currency_code: string;
            value: string;
          };
        };
      }>;
    };
  };
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
  event_version: string;
  create_time: string;
  resource_version: string;
}

// PayPal subscription status mapping
export const PAYPAL_STATUS_MAP = {
  APPROVAL_PENDING: 'pending',
  APPROVED: 'active',
  ACTIVE: 'active',
  SUSPENDED: 'suspended',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
} as const;

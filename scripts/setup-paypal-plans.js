/**
 * <PERSON><PERSON><PERSON> to create PayPal products and subscription plans
 * Run this once to set up the Pro and Plus subscription plans
 */

require('dotenv').config({ path: '.env.local' });

// PayPal configuration
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET;
const PAYPAL_ENVIRONMENT = process.env.PAYPAL_ENVIRONMENT || 'production';

if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
  console.error('❌ PayPal credentials not found in environment variables');
  process.exit(1);
}

// PayPal API base URL
const PAYPAL_BASE_URL = PAYPAL_ENVIRONMENT === 'production'
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

// Get access token for PayPal API
async function getAccessToken() {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');

  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    throw new Error(`Failed to get access token: ${response.statusText}`);
  }

  const data = await response.json();
  return data.access_token;
}

// Product configurations
const PRODUCTS = {
  SUBSCRIPTION_SERVICE: {
    name: 'The Consult Now - AI Resume Screening Service',
    description: 'AI-powered resume screening and job matching service for HR teams',
    type: 'SERVICE',
    category: 'SOFTWARE',
    image_url: 'https://theconsultnow.com/logo.png',
    home_url: 'https://theconsultnow.com'
  }
};

// Plan configurations
const PLANS = {
  PRO: {
    name: 'Pro Plan',
    description: 'Perfect for growing HR teams - 50 jobs per month, 100 resumes per job',
    price: '9.99',
    currency: 'USD'
  },
  PLUS: {
    name: 'Plus Plan',
    description: 'Perfect for bulk hiring - 100 jobs per month, 100 resumes per job',
    price: '19.99',
    currency: 'USD'
  }
};

async function createProduct() {
  console.log('🔄 Creating PayPal product...');

  try {
    const accessToken = await getAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/catalogs/products`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'PayPal-Request-Id': `product-${Date.now()}`,
      },
      body: JSON.stringify(PRODUCTS.SUBSCRIPTION_SERVICE),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to create product: ${response.statusText} - ${errorData}`);
    }

    const product = await response.json();
    console.log('✅ Product created successfully:');
    console.log(`   Product ID: ${product.id}`);
    console.log(`   Name: ${product.name}`);

    return product.id;
  } catch (error) {
    console.error('❌ Error creating product:', error);
    throw error;
  }
}

async function createSubscriptionPlan(productId, planType) {
  console.log(`🔄 Creating ${planType} subscription plan...`);

  const planConfig = PLANS[planType];

  const planRequest = {
    product_id: productId,
    name: planConfig.name,
    description: planConfig.description,
    status: 'ACTIVE',
    billing_cycles: [
      {
        frequency: {
          interval_unit: 'MONTH',
          interval_count: 1,
        },
        tenure_type: 'REGULAR',
        sequence: 1,
        total_cycles: 0, // 0 means infinite
        pricing_scheme: {
          fixed_price: {
            value: planConfig.price,
            currency_code: planConfig.currency,
          },
        },
      },
    ],
    payment_preferences: {
      auto_bill_outstanding: true,
      setup_fee_failure_action: 'CONTINUE',
      payment_failure_threshold: 3,
    },
    taxes: {
      percentage: '0',
      inclusive: false,
    },
  };

  try {
    const accessToken = await getAccessToken();

    const response = await fetch(`${PAYPAL_BASE_URL}/v1/billing/plans`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'PayPal-Request-Id': `plan-${planType}-${Date.now()}`,
      },
      body: JSON.stringify(planRequest),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to create ${planType} plan: ${response.statusText} - ${errorData}`);
    }

    const plan = await response.json();
    console.log(`✅ ${planType} plan created successfully:`);
    console.log(`   Plan ID: ${plan.id}`);
    console.log(`   Name: ${plan.name}`);
    console.log(`   Price: ${planConfig.currency} ${planConfig.price}/month`);

    return plan.id;
  } catch (error) {
    console.error(`❌ Error creating ${planType} plan:`, error);
    throw error;
  }
}

async function main() {
  console.log('🚀 Setting up PayPal subscription plans...');
  console.log(`Environment: ${PAYPAL_ENVIRONMENT}`);
  console.log('');
  
  try {
    // Step 1: Create product
    const productId = await createProduct();
    console.log('');
    
    // Step 2: Create Pro plan
    const proPlanId = await createSubscriptionPlan(productId, 'PRO');
    console.log('');
    
    // Step 3: Create Plus plan
    const plusPlanId = await createSubscriptionPlan(productId, 'PLUS');
    console.log('');
    
    // Step 4: Display results
    console.log('🎉 Setup completed successfully!');
    console.log('');
    console.log('📋 Update your lib/paypal.ts file with these Plan IDs:');
    console.log('');
    console.log('export const PAYPAL_PLANS = {');
    console.log(`  PRO: '${proPlanId}',`);
    console.log(`  PLUS: '${plusPlanId}',`);
    console.log('};');
    console.log('');
    console.log('🔗 You can view your plans in PayPal Dashboard:');
    if (PAYPAL_ENVIRONMENT === 'production') {
      console.log('   https://www.paypal.com/billing/plans');
    } else {
      console.log('   https://www.sandbox.paypal.com/billing/plans');
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
main();

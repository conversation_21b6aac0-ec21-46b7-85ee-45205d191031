"use client"
import { useState, useEffect } from "react"
import { User, Edit, Trash2, Crown, Calendar, Building, CreditCard, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { auth, db } from "@/lib/firebase"
import { updateProfile, deleteUser } from "firebase/auth"
import { doc, getDoc, updateDoc } from "firebase/firestore"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

export default function ProfilePage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [fullName, setFullName] = useState("")
  const [companyName, setCompanyName] = useState("")

  const [isSaving, setIsSaving] = useState(false)
  const [userUsageData, setUserUsageData] = useState<any>(null)
  const router = useRouter()
  const { toast } = useToast()

  // Check authentication and load user data
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      setIsLoading(false)
      if (user) {
        setIsAuthenticated(true)
        setFullName(user.displayName || "")

        // Load user usage data
        try {
          const userDoc = await getDoc(doc(db, "users", user.uid))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            setUserUsageData(userData)
            setCompanyName(userData.companyName || "")
          }
        } catch (error) {
          console.error("Error loading user data:", error)
        }
      } else {
        router.push("/login")
        toast({
          title: "Authentication required",
          description: "Please log in to access this page",
          variant: "destructive",
        })
      }
    })

    return () => unsubscribe()
  }, [router, toast])

  // Calculate next renewal date
  const getNextRenewalDate = () => {
    if (!userUsageData?.registrationDate) return "Not available"

    const registrationDate = userUsageData.registrationDate.toDate ?
      userUsageData.registrationDate.toDate() :
      new Date(userUsageData.registrationDate)

    const nextRenewal = new Date(registrationDate)
    nextRenewal.setMonth(nextRenewal.getMonth() + 1)

    return nextRenewal.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Save profile changes
  const handleSaveChanges = async () => {
    if (!auth.currentUser) return

    // Validate required fields
    if (!companyName.trim()) {
      toast({
        title: "Company name required",
        description: "Please enter your company name.",
        variant: "destructive",
      })
      return
    }



    setIsSaving(true)
    try {
      // Update Firebase Auth profile
      await updateProfile(auth.currentUser, {
        displayName: fullName
      })

      // Update Firestore user document
      await updateDoc(doc(db, "users", auth.currentUser.uid), {
        fullName,
        companyName: companyName.trim(),
      })

      setIsEditing(false)
      toast({
        title: "Profile updated",
        description: "Your profile has been successfully updated.",
      })
    } catch (error) {
      console.error("Error updating profile:", error)
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }



  // Delete account
  const handleDeleteAccount = async () => {
    if (!auth.currentUser) return

    try {
      await deleteUser(auth.currentUser)
      toast({
        title: "Account deleted",
        description: "Your account has been permanently deleted.",
      })
      router.push("/login")
    } catch (error) {
      console.error("Error deleting account:", error)
      toast({
        title: "Error",
        description: "Failed to delete account. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-[#1aa8e0] animate-spin" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-4xl mx-auto pt-12">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-[#1aa8e0]">Profile Settings</h1>
          <Button
            onClick={() => setIsEditing(!isEditing)}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Edit className="h-4 w-4 mr-2" />
            {isEditing ? "Cancel" : "Edit Profile"}
          </Button>
        </div>

        <div className="max-w-2xl mx-auto">
          {/* Usage Information */}
          <Card className="bg-zinc-900 border-zinc-700 mb-6">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Crown className="h-5 w-5 mr-2 text-yellow-500" />
                Usage Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-zinc-300">Current Tier</Label>
                  <div className="flex items-center mt-1">
                    <span className="text-yellow-500 font-medium">
                      {userUsageData?.tier || "Free"}
                    </span>
                  </div>
                </div>
                <div>
                  <Label className="text-zinc-300">Next Renewal</Label>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 mr-1 text-zinc-400" />
                    <span className="text-white text-sm">
                      {getNextRenewalDate()}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-3 pt-2">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <Label className="text-zinc-300">Job Descriptions</Label>
                    <span className="text-sm text-zinc-400">
                      {userUsageData?.jobDescriptionCount || 0}/{
                        userUsageData?.tier === 'Pro' ? '50' :
                        userUsageData?.tier === 'Plus' ? '100' : '3'
                      }
                    </span>
                  </div>
                  <div className="w-full bg-zinc-700 rounded-full h-2">
                    <div
                      className="bg-[#1aa8e0] h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min(((userUsageData?.jobDescriptionCount || 0) / (
                          userUsageData?.tier === 'Pro' ? 50 :
                          userUsageData?.tier === 'Plus' ? 100 : 3
                        )) * 100, 100)}%`
                      }}
                    />
                  </div>
                </div>

                <div className="text-xs text-zinc-500 bg-zinc-800 p-3 rounded-lg">
                  <div className="font-medium text-zinc-400 mb-1">
                    {userUsageData?.tier === 'Pro' ? 'Pro Tier Limits:' :
                     userUsageData?.tier === 'Plus' ? 'Plus Tier Limits:' : 'Free Tier Limits:'}
                  </div>
                  <div>• Job descriptions: {
                    userUsageData?.tier === 'Pro' ? '50' :
                    userUsageData?.tier === 'Plus' ? '100' : '3'
                  } maximum</div>
                  <div>• Resumes per job: {
                    userUsageData?.tier === 'Pro' || userUsageData?.tier === 'Plus' ? '100' : '50'
                  } maximum</div>
                  <div>• Data retention: {
                    userUsageData?.tier === 'Pro' || userUsageData?.tier === 'Plus' ? '180' : '60'
                  } days</div>
                  <div>• File size limit: 20 MB total per analysis</div>
                </div>
              </div>


            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card className="bg-zinc-900 border-zinc-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <User className="h-5 w-5 mr-2 text-[#1aa8e0]" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-zinc-300">Full Name</Label>
                <Input
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  disabled={!isEditing}
                  className="bg-zinc-800 border-zinc-600 text-white"
                />
              </div>
              <div>
                <Label className="text-zinc-300">Company Name *</Label>
                <div className="relative">
                  <Input
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter your company name"
                    className="bg-zinc-800 border-zinc-600 text-white pl-10"
                  />
                  <Building className="absolute left-3 top-1/2 -translate-y-1/2 text-zinc-400 h-4 w-4" />
                </div>

              </div>
              <div>
                <Label className="text-zinc-300">Account Email</Label>
                <Input
                  value={auth.currentUser?.email || ""}
                  disabled={true}
                  className="bg-zinc-800 border-zinc-600 text-white"
                />
              </div>
            </CardContent>
          </Card>



          {/* Delete Account Section */}
          <Card className="bg-zinc-900 border-zinc-700 mt-6">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Trash2 className="h-5 w-5 mr-2 text-red-500" />
                Danger Zone
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 bg-red-950/20 border border-red-800 rounded-lg">
                <h3 className="text-red-400 font-medium mb-2">Delete Account</h3>
                <p className="text-zinc-400 text-sm mb-4">
                  Once you delete your account, there is no going back. Please be certain.
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="bg-red-600 hover:bg-red-700">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Account
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-zinc-900 border-zinc-700">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-white">Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription className="text-zinc-400">
                        This action cannot be undone. This will permanently delete your account
                        and remove all your data from our servers.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className="bg-zinc-800 border-zinc-600 text-white hover:bg-zinc-700">
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction
                        className="bg-red-600 hover:bg-red-700"
                        onClick={handleDeleteAccount}
                      >
                        Yes, delete my account
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        </div>

        {isEditing && (
          <div className="mt-6 flex justify-end">
            <Button
              className="bg-[#1aa8e0] hover:bg-[#1aa8e0]/90 text-white"
              onClick={handleSaveChanges}
              disabled={isSaving}
            >
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

'use client'

import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useAuthState } from 'react-firebase-hooks/auth'
import { auth } from '@/lib/firebase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, ArrowLeft, Loader2 } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

// Plan configurations
const PLAN_CONFIGS = {
  pro: {
    name: 'Pro Plan',
    price: 9.99,
    description: 'Perfect for growing HR teams',
    features: [
      '50 jobs per month',
      '100 resumes per job',
      'AI Screening',
      'CSV Export',
      'Resume Database',
      'Priority support',
      'Delete Resume after 180 days',
      '1 user only',
    ],
    badge: 'POPULAR',
    badgeColor: 'bg-[#1aa8e0]',
  },
  plus: {
    name: 'Plus Plan',
    price: 19.99,
    description: 'Perfect for bulk hiring',
    features: [
      '100 jobs per month',
      '100 resumes per job',
      'AI Screening',
      'CSV Export',
      'Resume Database',
      'Priority support',
      'Delete Resume after 180 days',
      '1 user only',
    ],
    badge: 'BEST VALUE',
    badgeColor: 'bg-[#8529db]',
  },
}

export default function CheckoutPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [user, loading, error] = useAuthState(auth)
  const [isCreatingSubscription, setIsCreatingSubscription] = useState(false)
  
  const tier = params.tier as string
  const planConfig = PLAN_CONFIGS[tier as keyof typeof PLAN_CONFIGS]

  // Redirect if invalid tier
  useEffect(() => {
    if (!loading && (!tier || !planConfig)) {
      router.push('/pricing')
    }
  }, [tier, planConfig, loading, router])

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/signin?redirect=' + encodeURIComponent(`/checkout/${tier}`))
    }
  }, [user, loading, tier, router])

  const handleSubscribe = async () => {
    if (!user) {
      router.push('/signin?redirect=' + encodeURIComponent(`/checkout/${tier}`))
      return
    }

    setIsCreatingSubscription(true)

    try {
      // Create PayPal subscription
      const response = await fetch('/api/paypal/create-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`,
        },
        body: JSON.stringify({
          tier: tier.charAt(0).toUpperCase() + tier.slice(1), // Capitalize first letter
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create subscription')
      }

      // Redirect to PayPal for approval
      if (data.approvalUrl) {
        window.location.href = data.approvalUrl
      } else {
        throw new Error('No approval URL received from PayPal')
      }

    } catch (error) {
      console.error('Error creating subscription:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create subscription',
        variant: 'destructive',
      })
    } finally {
      setIsCreatingSubscription(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-[#1aa8e0]" />
      </div>
    )
  }

  if (!planConfig) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#8529db] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-[#1aa8e0] rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-[#8529db] rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-12">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => router.push('/pricing')}
          className="mb-8 text-gray-400 hover:text-white"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Pricing
        </Button>

        <div className="max-w-2xl mx-auto">
          {/* Plan Summary Card */}
          <Card className="bg-black/40 backdrop-blur-xl border-gray-700/30 mb-8">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl text-white">{planConfig.name}</CardTitle>
                  <CardDescription className="text-gray-400 mt-1">
                    {planConfig.description}
                  </CardDescription>
                </div>
                <Badge className={`${planConfig.badgeColor} text-white border-0`}>
                  {planConfig.badge}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {/* Price */}
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">${planConfig.price}</span>
                <span className="text-gray-400 text-lg">/month</span>
              </div>

              {/* Features */}
              <div className="space-y-3 mb-8">
                {planConfig.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-[#1aa8e0] flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Subscribe Button */}
              <Button
                onClick={handleSubscribe}
                disabled={isCreatingSubscription}
                className="w-full py-3 bg-[#1aa8e0] text-white hover:bg-[#1aa8e0]/90 border-0"
              >
                {isCreatingSubscription ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating Subscription...
                  </>
                ) : (
                  'Subscribe with PayPal'
                )}
              </Button>

              {/* Security Note */}
              <p className="text-xs text-gray-500 text-center mt-4">
                Secure payment powered by PayPal. Cancel anytime.
              </p>
            </CardContent>
          </Card>

          {/* Additional Info */}
          <div className="text-center text-gray-400 text-sm">
            <p>
              By subscribing, you agree to our{' '}
              <a href="/terms" className="text-[#1aa8e0] hover:underline">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="/privacy" className="text-[#1aa8e0] hover:underline">
                Privacy Policy
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
